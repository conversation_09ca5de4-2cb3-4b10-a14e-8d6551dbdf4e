import { useState } from 'react';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <>
      {/* Mobile Overlay */}
      {isMenuOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
          onClick={closeMenu}
        />
      )}

      {/* Main Navbar */}
      <nav className="fixed top-3 left-1/2 transform -translate-x-1/2 z-50 w-[calc(100%-1.5rem)] max-w-7xl">
        <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl shadow-black/20">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16 lg:h-18">
              {/* Logo */}
              <div className="flex-shrink-0">
                <div className="text-xl lg:text-2xl font-bold">
                  <span className="text-white/90">Novix</span>
                  <span className="text-red-700"> Studios</span>
                </div>
              </div>

              {/* Desktop Navigation */}
              <div className="hidden lg:flex items-center space-x-1">
                {['HOME', 'SERVICES', 'PORTFOLIO', 'ABOUT', 'CONTACT'].map((item, index) => (
                  <div key={item} className="group relative">
                    <button className="flex items-center space-x-1 text-white/70 hover:text-white px-3 py-2 rounded-xl cursor-pointer transition-all duration-300 hover:backdrop-blur-lg">
                      <div className="absolute inset-0 bg-white/0 group-hover:bg-white/10 rounded-xl transition-all duration-300 border border-transparent group-hover:border-white/20"></div>
                      <span className="relative font-medium tracking-wide text-sm">{item}</span>
                      {index < 4 && (
                        <svg className="relative w-4 h-4 ml-1 group-hover:translate-y-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      )}
                    </button>
                  </div>
                ))}
              </div>

              {/* Right Side - CTA Button & Mobile Menu */}
              <div className="flex items-center space-x-4">
                {/* CTA Button - Desktop */}
                <div className="hidden lg:block">
                  <button className="group inline-flex items-center bg-gradient-to-r from-red-700/80 to-red-800/80 backdrop-blur-sm border border-red-700/50 rounded-xl shadow-lg hover:shadow-red-800/40 hover:shadow-2xl hover:from-red-800 hover:to-red-900 transition-all duration-300 hover:scale-105 hover:-translate-y-0.5">
                    <div className="bg-white/20 group-hover:bg-white/40 transition-all duration-300 p-2 rounded-lg m-1 group-hover:rotate-12">
                      <svg className="w-4 h-4 text-white group-hover:translate-x-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                      </svg>
                    </div>
                    <span className="px-3 py-2 text-white font-semibold text-sm whitespace-nowrap group-hover:text-white/95 transition-colors duration-300">
                      Join Novix Studios
                    </span>
                  </button>
                </div>

                {/* Mobile Menu Button */}
                <div className="lg:hidden">
                  <button
                    onClick={toggleMenu}
                    className="group relative text-white/70 hover:text-white hover:bg-white/10 focus:outline-none focus:text-white p-2 rounded-xl transition-all duration-300 hover:backdrop-blur-lg border border-transparent hover:border-white/20"
                    aria-label="Toggle menu"
                  >
                    <div className="w-6 h-6 relative">
                      <span className={`absolute left-0 top-1 w-6 h-0.5 bg-current transition-all duration-300 ${isMenuOpen ? 'rotate-45 translate-y-2' : ''}`}></span>
                      <span className={`absolute left-0 top-3 w-6 h-0.5 bg-current transition-all duration-300 ${isMenuOpen ? 'opacity-0' : ''}`}></span>
                      <span className={`absolute left-0 top-5 w-6 h-0.5 bg-current transition-all duration-300 ${isMenuOpen ? '-rotate-45 -translate-y-2' : ''}`}></span>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Mobile Navigation Menu */}
          <div className={`lg:hidden absolute top-full left-0 right-0 mt-2 transition-all duration-300 ${isMenuOpen ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-4 pointer-events-none'}`}>
            <div className="mx-4 bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl shadow-2xl shadow-black/20 overflow-hidden">
              <div className="px-6 py-4 space-y-1">
                {['HOME', 'SERVICES', 'PORTFOLIO', 'ABOUT', 'CONTACT'].map((item) => (
                  <button
                    key={item}
                    onClick={closeMenu}
                    className="group relative w-full text-left text-white/80 hover:text-white hover:bg-white/15 block px-4 py-3 rounded-xl text-base font-medium transition-all duration-300 hover:backdrop-blur-lg border border-transparent hover:border-white/20"
                  >
                    {item}
                  </button>
                ))}

                {/* Mobile CTA Button */}
                <div className="pt-4 border-t border-white/10">
                  <button
                    onClick={closeMenu}
                    className="group w-full inline-flex items-center justify-center bg-gradient-to-r from-red-800/80 to-red-900/80 backdrop-blur-sm border border-red-700/50 rounded-xl shadow-lg hover:shadow-red-800/40 hover:shadow-2xl hover:from-red-800 hover:to-red-900 transition-all duration-300 hover:scale-105 py-3 px-4"
                  >
                    <div className="bg-white/20 group-hover:bg-white/40 transition-all duration-300 p-2 rounded-lg mr-3 group-hover:rotate-12">
                      <svg className="w-4 h-4 text-white group-hover:translate-x-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                      </svg>
                    </div>
                    <span className="text-white font-semibold text-sm group-hover:text-white/95 transition-colors duration-300">
                      Get Started
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </nav>
    </>
  );
};

export default Navbar;
