import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { AnimatedTooltip } from './ui/animated-tooltip';

const About = () => {
  const scrollTextRef = useRef(null);

  // Sample team data - replace with actual team member data
  const teamMembers = [
    {
      id: 1,
      name: "<PERSON>",
      designation: "Creative Director",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80"
    },
    {
      id: 2,
      name: "<PERSON>",
      designation: "Brand Strategist",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80"
    },
    {
      id: 3,
      name: "<PERSON>",
      designation: "Digital Marketing Lead",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
    },
    {
      id: 4,
      name: "David Kim",
      designation: "UX/UI Designer",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
    },
    {
      id: 5,
      name: "Lisa Thompson",
      designation: "Content Strategist",
      image: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=688&q=80"
    }
  ];

  useEffect(() => {
    // GSAP animation for seamless scrolling text
    const scrollText = scrollTextRef.current;
    if (scrollText) {
      // Set initial position
      gsap.set(scrollText, { x: '0%' });

      // Create seamless infinite scroll - move exactly one-third of the total width
      // Since we have 3 complete sets, moving by 33.33% reveals the next identical set
      gsap.to(scrollText, {
        x: '-33.33%', // Move one-third of the total width for seamless loop
        duration: 12, // Adjusted duration for smooth scrolling
        ease: 'none',
        repeat: -1,
      });
    }
  }, []);

  return (
    <section className="relative min-h-screen bg-black flex flex-col overflow-hidden">
      {/* Modern Metallic Background Design */}
      <div className="absolute inset-0">
        {/* Primary metallic silver beam - diagonal sweep */}
        <div className="absolute -top-32 -left-32 w-96 h-[800px] bg-gradient-to-br from-slate-200/20 via-slate-300/15 to-transparent rotate-12 blur-2xl opacity-80"></div>

        {/* Secondary metallic red accent beam */}
        <div className="absolute -bottom-32 -right-32 w-80 h-[700px] bg-gradient-to-tl from-red-500/25 via-red-600/20 to-transparent -rotate-12 blur-2xl opacity-70"></div>

        {/* Central metallic highlight orb */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full blur-3xl animate-pulse" style={{
          background: 'radial-gradient(circle, rgba(241, 245, 249, 0.08) 0%, rgba(148, 163, 184, 0.12) 40%, rgba(100, 116, 139, 0.08) 70%, transparent 100%)'
        }}></div>

        {/* Floating metallic particles */}
        <div className="absolute top-1/4 left-1/4 w-4 h-4 bg-slate-300/40 rounded-full blur-sm animate-bounce" style={{animationDelay: '0s', animationDuration: '3s'}}></div>
        <div className="absolute top-3/4 right-1/4 w-3 h-3 bg-red-400/50 rounded-full blur-sm animate-bounce" style={{animationDelay: '1s', animationDuration: '4s'}}></div>
        <div className="absolute top-1/3 right-1/3 w-2 h-2 bg-slate-200/60 rounded-full blur-sm animate-bounce" style={{animationDelay: '2s', animationDuration: '5s'}}></div>
        <div className="absolute bottom-1/4 left-1/3 w-3 h-3 bg-red-300/40 rounded-full blur-sm animate-bounce" style={{animationDelay: '0.5s', animationDuration: '3.5s'}}></div>

        {/* Metallic mesh overlay */}
        <div className="absolute inset-0 opacity-[0.03]" style={{
          backgroundImage: `
            linear-gradient(90deg, rgba(148, 163, 184, 0.5) 1px, transparent 1px),
            linear-gradient(rgba(148, 163, 184, 0.5) 1px, transparent 1px)
          `,
          backgroundSize: '60px 60px'
        }}></div>

        {/* Subtle noise texture for metallic feel */}
        <div className="absolute inset-0 opacity-[0.02] mix-blend-overlay" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
        }}></div>

        {/* Gradient fade to black at bottom */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black via-black/80 to-transparent"></div>
      </div>

      {/* Main Content Area */}
      <div className="relative z-10 flex-1 flex items-center justify-center px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12 lg:py-16">
        <div className="max-w-7xl mx-auto w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 md:gap-10 lg:gap-12 xl:gap-16 items-center h-full">
            {/* Left Content - Large Number */}
            <div className="text-center lg:text-left order-2 lg:order-1">
              <div className="text-[4rem] xs:text-[5rem] sm:text-[6rem] md:text-[8rem] lg:text-[10rem] xl:text-[12rem] 2xl:text-[16rem] font-bold bg-gradient-to-br from-slate-300 via-slate-400 to-slate-600 bg-clip-text text-transparent leading-none mb-2 sm:mb-3 md:mb-4">24+</div>
              <h3 className="text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-5xl xl:text-6xl font-semibold text-white/90 italic mb-3 sm:mb-4 md:mb-6 lg:mb-8" style={{ fontFamily: 'Playfair Display, serif' }}>EXPERIENCE</h3>

              {/* Team Members with Animated Tooltips */}
              <div className="flex flex-col sm:flex-row justify-center lg:justify-start items-center gap-3 sm:gap-4 lg:gap-6 mb-4 sm:mb-6">
                <div className="flex justify-center">
                  <AnimatedTooltip items={teamMembers} />
                </div>
                <p className="text-white/60 text-xs sm:text-sm md:text-base lg:text-lg font-medium text-center sm:text-left">
                  Business-Focused Social Media Techniques.
                </p>
              </div>
            </div>

            {/* Right Content - Service Categories in Row */}
            <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 gap-3 sm:gap-4 md:gap-5 order-1 lg:order-2">
              <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl pl-2 pr-3 py-3 sm:pl-3 sm:pr-4 sm:py-4 md:pl-3 md:pr-5 md:py-5 hover:bg-white/10 transition-all duration-300 shadow-2xl hover:shadow-white/10 hover:scale-105">
                <h4 className="text-white/90 font-semibold mb-2 sm:mb-3 text-xs sm:text-sm md:text-base">{"{ui/ux Design}"}</h4>
                <div className="w-full h-28 xs:h-32 sm:h-36 md:h-40 lg:h-44 rounded-xl border border-white/10 overflow-hidden">
                  <img
                    src="https://images.unsplash.com/photo-1561070791-2526d30994b5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                    alt="UI/UX Design"
                    className="w-full h-full object-cover hover:scale-110 transition-transform duration-500"
                  />
                </div>
              </div>
              <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl pl-2 pr-3 py-3 sm:pl-3 sm:pr-4 sm:py-4 md:pl-3 md:pr-5 md:py-5 hover:bg-white/10 transition-all duration-300 shadow-2xl hover:shadow-white/10 hover:scale-105">
                <h4 className="text-white/90 font-semibold mb-2 sm:mb-3 text-xs sm:text-sm md:text-base">{"{Branding}"}</h4>
                <div className="w-full h-28 xs:h-32 sm:h-36 md:h-40 lg:h-44 rounded-xl border border-white/10 overflow-hidden">
                  <img
                    src="https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                    alt="Branding"
                    className="w-full h-full object-cover hover:scale-110 transition-transform duration-500"
                  />
                </div>
              </div>
              <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl pl-2 pr-3 py-3 sm:pl-3 sm:pr-4 sm:py-4 md:pl-3 md:pr-5 md:py-5 hover:bg-white/10 transition-all duration-300 shadow-2xl hover:shadow-white/10 hover:scale-105 xs:col-span-2 sm:col-span-1">
                <h4 className="text-white/90 font-semibold mb-2 sm:mb-3 text-xs sm:text-sm md:text-base">{"{Development}"}</h4>
                <div className="w-full h-28 xs:h-32 sm:h-36 md:h-40 lg:h-44 rounded-xl border border-white/10 overflow-hidden">
                  <img
                    src="https://images.unsplash.com/photo-1461749280684-dccba630e2f6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                    alt="Development"
                    className="w-full h-full object-cover hover:scale-110 transition-transform duration-500"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Scrolling Text */}
      <div className="relative overflow-hidden">
        <div
          ref={scrollTextRef}
          className="flex items-center whitespace-nowrap py-4 sm:py-6"
          style={{ width: '300%' }}
        >
          {/* First complete set */}
          <div className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-8xl font-bold mx-6 sm:mx-8 md:mx-10 lg:mx-12"
               style={{WebkitTextStroke: '1px white', color: 'transparent'}}>
            MARKETING STUDIO
          </div>

          {/* Decorative Star Shape - Using SVG for better visibility */}
          <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 mx-3 sm:mx-4 md:mx-5 lg:mx-6 flex-shrink-0 relative">
            <svg viewBox="0 0 24 24" className="w-full h-full" fill="none" stroke="white" strokeWidth="2">
              <polygon points="12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26 12,2" />
            </svg>
          </div>

          <div className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-8xl font-bold mx-6 sm:mx-8 md:mx-10 lg:mx-12"
               style={{WebkitTextStroke: '1px white', color: 'transparent'}}>
            BRAND GUIDE
          </div>

          {/* Decorative Star Shape - Using SVG for better visibility */}
          <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 mx-3 sm:mx-4 md:mx-5 lg:mx-6 flex-shrink-0 relative">
            <svg viewBox="0 0 24 24" className="w-full h-full" fill="none" stroke="white" strokeWidth="2">
              <polygon points="12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26 12,2" />
            </svg>
          </div>

          <div className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-8xl font-bold mx-6 sm:mx-8 md:mx-10 lg:mx-12"
               style={{WebkitTextStroke: '1px white', color: 'transparent'}}>
            DIGITAL AGENCY
          </div>

          {/* Decorative Star Shape - Using SVG for better visibility */}
          <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 mx-3 sm:mx-4 md:mx-5 lg:mx-6 flex-shrink-0 relative">
            <svg viewBox="0 0 24 24" className="w-full h-full" fill="none" stroke="white" strokeWidth="2">
              <polygon points="12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26 12,2" />
            </svg>
          </div>

          {/* Second complete set for seamless loop */}
          <div className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-8xl font-bold mx-6 sm:mx-8 md:mx-10 lg:mx-12"
               style={{WebkitTextStroke: '1px white', color: 'transparent'}}>
            MARKETING STUDIO
          </div>

          {/* Decorative Star Shape - Using SVG for better visibility */}
          <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 mx-3 sm:mx-4 md:mx-5 lg:mx-6 flex-shrink-0 relative">
            <svg viewBox="0 0 24 24" className="w-full h-full" fill="none" stroke="white" strokeWidth="2">
              <polygon points="12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26 12,2" />
            </svg>
          </div>
          <div className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-8xl font-bold mx-6 sm:mx-8 md:mx-10 lg:mx-12"
               style={{WebkitTextStroke: '1px white', color: 'transparent'}}>
            BRAND GUIDE
          </div>

          {/* Decorative Star Shape - Using SVG for better visibility */}
          <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 mx-3 sm:mx-4 md:mx-5 lg:mx-6 flex-shrink-0 relative">
            <svg viewBox="0 0 24 24" className="w-full h-full" fill="none" stroke="white" strokeWidth="2">
              <polygon points="12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26 12,2" />
            </svg>
          </div>

          <div className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-8xl font-bold mx-6 sm:mx-8 md:mx-10 lg:mx-12"
               style={{WebkitTextStroke: '1px white', color: 'transparent'}}>
            DIGITAL AGENCY
          </div>

          {/* Decorative Star Shape - Using SVG for better visibility */}
          <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 mx-3 sm:mx-4 md:mx-5 lg:mx-6 flex-shrink-0 relative">
            <svg viewBox="0 0 24 24" className="w-full h-full" fill="none" stroke="white" strokeWidth="2">
              <polygon points="12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26 12,2" />
            </svg>
          </div>

          {/* Third complete set for extra smooth looping */}
          <div className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-8xl font-bold mx-6 sm:mx-8 md:mx-10 lg:mx-12"
               style={{WebkitTextStroke: '1px white', color: 'transparent'}}>
            MARKETING STUDIO
          </div>

          {/* Decorative Star Shape - Using SVG for better visibility */}
          <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 mx-3 sm:mx-4 md:mx-5 lg:mx-6 flex-shrink-0 relative">
            <svg viewBox="0 0 24 24" className="w-full h-full" fill="none" stroke="white" strokeWidth="2">
              <polygon points="12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26 12,2" />
            </svg>
          </div>

          <div className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-8xl font-bold mx-6 sm:mx-8 md:mx-10 lg:mx-12"
               style={{WebkitTextStroke: '1px white', color: 'transparent'}}>
            BRAND GUIDE
          </div>

          {/* Decorative Star Shape - Using SVG for better visibility */}
          <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 mx-3 sm:mx-4 md:mx-5 lg:mx-6 flex-shrink-0 relative">
            <svg viewBox="0 0 24 24" className="w-full h-full" fill="none" stroke="white" strokeWidth="2">
              <polygon points="12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26 12,2" />
            </svg>
          </div>

          <div className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-8xl font-bold mx-6 sm:mx-8 md:mx-10 lg:mx-12"
               style={{WebkitTextStroke: '1px white', color: 'transparent'}}>
            DIGITAL AGENCY
          </div>

          {/* Decorative Star Shape - Using SVG for better visibility */}
          <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 mx-3 sm:mx-4 md:mx-5 lg:mx-6 flex-shrink-0 relative">
            <svg viewBox="0 0 24 24" className="w-full h-full" fill="none" stroke="white" strokeWidth="2">
              <polygon points="12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26 12,2" />
            </svg>
          </div>

        </div>
      </div>
    </section>
  );
};

export default About;
