import { useRef, useEffect } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

const SplitText = ({
  text,
  className = "",
  delay = 100,
  duration = 0.6,
  ease = "power3.out",
  splitType = "chars",
  from = { opacity: 0, y: 40 },
  to = { opacity: 1, y: 0 },
  threshold = 0.1,
  rootMargin = "-100px",
  textAlign = "center",
  onLetterAnimationComplete,
}) => {
  const ref = useRef(null);
  const animationCompletedRef = useRef(false);

  // Custom text splitting function
  const splitText = (text, type) => {
    switch (type) {
      case "chars":
        return text.split("").map((char, i) => ({ char, index: i }));
      case "words":
        return text.split(" ").map((word, i) => ({ word, index: i }));
      case "lines":
        return text.split("\n").map((line, i) => ({ line, index: i }));
      default:
        return text.split("").map((char, i) => ({ char, index: i }));
    }
  };

  useEffect(() => {
    const el = ref.current;
    if (!el || animationCompletedRef.current) return;

    // Create split elements
    const splitData = splitText(text, splitType);
    el.innerHTML = "";

    let targets = [];

    if (splitType === "chars") {
      splitData.forEach(({ char, index }) => {
        const span = document.createElement("span");
        span.textContent = char === " " ? "\u00A0" : char; // Non-breaking space
        span.style.display = "inline-block";
        span.style.willChange = "transform, opacity";
        el.appendChild(span);
        targets.push(span);
      });
    } else if (splitType === "words") {
      splitData.forEach(({ word, index }) => {
        const span = document.createElement("span");
        span.textContent = word;
        span.style.display = "inline-block";
        span.style.willChange = "transform, opacity";
        el.appendChild(span);
        targets.push(span);

        // Add space after word (except last word)
        if (index < splitData.length - 1) {
          const space = document.createElement("span");
          space.textContent = "\u00A0";
          space.style.display = "inline-block";
          el.appendChild(space);
        }
      });
    } else {
      // Fallback: just show the text
      el.textContent = text;
      targets = [el];
    }

    const startPct = (1 - threshold) * 100;
    const m = /^(-?\d+)px$/.exec(rootMargin);
    const raw = m ? parseInt(m[1], 10) : 0;
    const sign = raw < 0 ? `-=${Math.abs(raw)}px` : `+=${raw}px`;
    const start = `top ${startPct}%${sign}`;

    // Set initial state immediately
    gsap.set(targets, { ...from, force3D: true });

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: el,
        start,
        toggleActions: "play none none none",
        once: true,
        markers: false, // Set to true for debugging
      },
      smoothChildTiming: true,
      onComplete: () => {
        animationCompletedRef.current = true;
        gsap.set(targets, {
          ...to,
          clearProps: "willChange",
          immediateRender: true,
        });
        onLetterAnimationComplete?.();
      },
    });

    tl.to(targets, {
      ...to,
      duration,
      ease,
      stagger: delay / 1000,
      force3D: true,
    });

    return () => {
      tl.kill();
      ScrollTrigger.getAll().forEach((t) => t.kill());
      gsap.killTweensOf(targets);
    };
  }, [
    text,
    delay,
    duration,
    ease,
    splitType,
    from,
    to,
    threshold,
    rootMargin,
    onLetterAnimationComplete,
  ]);

  return (
    <div
      ref={ref}
      className={`split-parent inline-block whitespace-normal ${className}`}
      style={{
        textAlign,
        wordWrap: "break-word",
      }}
    >
      {text}
    </div>
  );
};

export default SplitText;
