import { Pin<PERSON>ontainer } from "./ui/3d-pin";

const Services = () => {
  const services = [
    {
      id: 1,
      title: "UI/UX DESIGN.",
      category: "BRANDING",
      description: "Creating intuitive and engaging user experiences that drive results.",
      icon: (
        <svg className="w-full h-full text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      ),
      href: "#ui-ux"
    },
    {
      id: 2,
      title: "CREATIVE DESIGN.",
      category: "BRANDING",
      description: "Innovative creative solutions that capture your brand essence.",
      icon: (
        <svg className="w-full h-full text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
        </svg>
      ),
      href: "#creative"
    },
    {
      id: 3,
      title: "PRODUCT DESIGN.",
      category: "BRANDING",
      description: "End-to-end product design from concept to market-ready solutions.",
      icon: (
        <svg className="w-full h-full text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
        </svg>
      ),
      href: "#product"
    }
  ];

  return (
    <section className="relative min-h-screen bg-black flex flex-col overflow-hidden">

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex flex-col pt-8 md:pt-12">
        {/* Header */}
        <div className="flex flex-col lg:flex-row justify-between items-center lg:items-center flex-shrink-0 text-center lg:text-left">
          <div className="mb-4 lg:mb-0">
            <h2 className="text-2xl md:text-4xl lg:text-6xl font-bold text-white mb-0">
              DISCOVER OUR
            </h2>
            <h3 className="text-2xl md:text-4xl lg:text-6xl font-bold italic text-white metallic-text" style={{ fontFamily: 'Playfair Display, serif' }}>
              EXPERTISE.
            </h3>
          </div>

          <div className="flex-shrink-0">
            <button className="group inline-flex items-center bg-gradient-to-r from-red-700/80 to-red-800/80 backdrop-blur-sm border border-red-700/50 rounded-xl shadow-lg hover:shadow-red-800/40 hover:shadow-2xl hover:from-red-800 hover:to-red-900 transition-all duration-300 hover:scale-105 hover:-translate-y-0.5 px-4 md:px-6 py-2.5 md:py-3">
              <span className="text-white font-medium mr-2 text-sm md:text-base">View All Services</span>
              <div className="bg-white/20 group-hover:bg-white/40 transition-all duration-300 p-1.5 md:p-2 rounded-lg group-hover:rotate-12">
                <svg className="w-3.5 md:w-4 h-3.5 md:h-4 text-white group-hover:translate-x-0.5 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </div>
            </button>
          </div>
        </div>``

        {/* Services Flex */}
        <div className="flex-1 flex items-center justify-center">
          <div className="flex flex-col md:flex-row items-center justify-center gap-6 md:gap-12 lg:gap-16 w-full max-w-7xl">
            {services.map((service) => (
              <div key={service.id} className="h-[28rem] md:h-[45rem] flex items-center justify-center w-full md:w-auto max-w-[18rem] md:max-w-none">
                <PinContainer
                  title={service.category}
                  href={service.href}
                  containerClassName="w-full mx-auto"
                >
                  <div className="flex basis-full flex-col p-4 md:p-8 tracking-tight text-slate-100/50 w-[16rem] md:w-[22rem] h-[16rem] md:h-[24rem]">
                  {/* Category Badge */}
                  <div className="mb-3 md:mb-6 flex justify-center md:justify-start">
                    <span className="inline-block px-3 md:px-4 py-1 md:py-2 text-xs font-bold text-white/90 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 shadow-lg">
                      {service.category}
                    </span>
                  </div>

                  {/* Icon */}
                  <div className="mb-4 md:mb-8 p-2 md:p-3 rounded-2xl bg-white/10 backdrop-blur-md border border-white/20 w-fit mx-auto md:mx-0 shadow-lg hover:bg-white/15 transition-all duration-300">
                    <div className="w-5 md:w-8 h-5 md:h-8 text-white/80">
                      {service.icon}
                    </div>
                  </div>

                  {/* Title */}
                  <h3 className="!pb-3 md:!pb-6 !m-0 font-bold text-lg md:text-2xl text-white leading-tight tracking-wide text-center md:text-left">
                    {service.title}
                  </h3>

                  {/* Description */}
                  <div className="text-xs md:text-base !m-0 !p-0 font-normal flex-1">
                    <p className="text-white/70 leading-relaxed text-center md:text-left">
                      {service.description}
                    </p>
                  </div>
                  </div>
                </PinContainer>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
