import React from 'react';
import { TestimonialCarousel } from './ui/testimonial-carousel';

const CarouselSection = () => {
  // You can customize these testimonials or pass them as props
  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      title: "UX Designer, Research",
      company: "HubSpot",
      companyLogo: "https://cdn.worldvectorlogo.com/logos/hubspot.svg",
      image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
      rating: 5,
      text: "Mauris quis nunc sollicitudin leo convallis lectus tempor diam elementum ellus diam lectus Mauris eu ultrices libero non euismod arcu orci nulla eleiend libero sed maximus bland diam malesuada phareoa sodalez egestas mi arcu ex congue ax mattis neque nlemenum quam."
    },
    {
      id: 2,
      name: "<PERSON>",
      title: "Product Manager",
      company: "Google",
      companyLogo: "https://cdn.worldvectorlogo.com/logos/google-icon.svg",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
      rating: 5,
      text: "Outstanding work quality and attention to detail. The team delivered beyond our expectations and helped us achieve remarkable results in our digital transformation journey. Their innovative approach transformed our entire workflow."
    },
    {
      id: 3,
      name: "Michael Chen",
      title: "Creative Director",
      company: "Adobe",
      companyLogo: "https://cdn.worldvectorlogo.com/logos/adobe-1.svg",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80",
      rating: 5,
      text: "Exceptional creativity and professional execution. Their innovative approach to design and branding has significantly elevated our brand presence in the market. The results exceeded all our expectations."
    },
    {
      id: 4,
      name: "Emily Rodriguez",
      title: "Marketing Director",
      company: "Microsoft",
      companyLogo: "https://cdn.worldvectorlogo.com/logos/microsoft-5.svg",
      image: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=688&q=80",
      rating: 5,
      text: "The strategic insights and creative solutions provided by this team have been instrumental in our success. Their attention to detail and commitment to excellence is truly remarkable."
    }
  ];

  return (
    <section className="relative min-h-screen bg-black flex items-center justify-center overflow-hidden py-16 px-4">
      {/* Background Design - Matching your existing theme */}
      <div className="absolute inset-0">
        {/* Primary metallic silver beam */}
        <div className="absolute -top-32 -right-32 w-96 h-[800px] bg-gradient-to-bl from-slate-200/15 via-slate-300/10 to-transparent -rotate-12 blur-2xl opacity-60"></div>
        
        {/* Secondary metallic accent beam */}
        <div className="absolute -bottom-32 -left-32 w-80 h-[700px] bg-gradient-to-tr from-red-500/20 via-red-600/15 to-transparent rotate-12 blur-2xl opacity-50"></div>
        
        {/* Central highlight orb */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[500px] h-[500px] rounded-full blur-3xl animate-pulse" style={{
          background: 'radial-gradient(circle, rgba(241, 245, 249, 0.06) 0%, rgba(148, 163, 184, 0.08) 40%, rgba(100, 116, 139, 0.06) 70%, transparent 100%)'
        }}></div>
        
        {/* Floating particles */}
        <div className="absolute top-1/4 left-1/4 w-3 h-3 bg-slate-300/30 rounded-full blur-sm animate-bounce" style={{animationDelay: '0s', animationDuration: '4s'}}></div>
        <div className="absolute top-3/4 right-1/4 w-2 h-2 bg-red-400/40 rounded-full blur-sm animate-bounce" style={{animationDelay: '2s', animationDuration: '5s'}}></div>
        <div className="absolute top-1/3 right-1/3 w-4 h-4 bg-slate-200/40 rounded-full blur-sm animate-bounce" style={{animationDelay: '1s', animationDuration: '3s'}}></div>
        
        {/* Subtle mesh overlay */}
        <div className="absolute inset-0 opacity-[0.02]" style={{
          backgroundImage: `
            linear-gradient(90deg, rgba(148, 163, 184, 0.3) 1px, transparent 1px),
            linear-gradient(rgba(148, 163, 184, 0.3) 1px, transparent 1px)
          `,
          backgroundSize: '80px 80px'
        }}></div>
      </div>

      {/* Content Container */}
      <div className="relative z-10 w-full max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-br from-slate-200 via-slate-300 to-slate-500 bg-clip-text text-transparent mb-6">
            Client Testimonials
          </h2>
          <p className="text-slate-400 text-lg md:text-xl max-w-3xl mx-auto">
            Hear what our clients say about their experience working with us
          </p>
        </div>

        {/* Testimonial Carousel */}
        <TestimonialCarousel testimonials={testimonials} />
      </div>
    </section>
  );
};

export default CarouselSection;
