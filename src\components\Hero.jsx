import Beams from './ui/beams'
import imgLogo from '../assets/img.png'

const Hero = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center bg-slate-900 px-4 sm:px-6 lg:px-8">
      <div className="absolute inset-0">
        <Beams
          beamWidth={2}
          beamHeight={30}
          beamNumber={12}
          lightColor="#ffffff"
          speed={2}
          noiseIntensity={1.75}
          scale={0.2}
          rotation={45}
        />
      </div>

      {/* Gradient overlay at the bottom */}
      <div className="absolute inset-x-0 bottom-0 h-24 sm:h-32 bg-gradient-to-t from-black via-black/80 to-transparent z-5"></div>

      <div className="relative z-10 text-center max-w-6xl mx-auto w-full">
        <img
          src={imgLogo}
          alt="Logo"
          className="mx-auto mb-4 sm:mb-6 w-full max-w-xs sm:max-w-md md:max-w-lg lg:max-w-2xl h-auto"
        />
        <p className="text-md sm:text-xl md:text-2xl lg:text-3xl text-white font-medium px-4">
          Creative solution for modern brands
        </p>
      </div>
    </section>
  )
}

export default Hero
