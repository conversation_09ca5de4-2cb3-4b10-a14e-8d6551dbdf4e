import React, { useEffect, useRef } from 'react';
import { ClickSpark } from './ui';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const Connecting = () => {
  const connectingRef = useRef(null);
  const brandsRef = useRef(null);
  const customersRef = useRef(null);
  const descriptionRef = useRef(null);

  useEffect(() => {
    // Function to split text into characters
    const splitTextIntoChars = (element, isGradient = false) => {
      const text = element.textContent;
      element.innerHTML = '';

      return text.split('').map((char, index) => {
        const span = document.createElement('span');
        span.textContent = char === ' ' ? '\u00A0' : char;
        span.style.display = 'inline-block';
        span.style.opacity = '0';
        span.style.transform = 'translateY(40px)';

        // Handle gradient text specially
        if (isGradient) {
          span.style.background = 'linear-gradient(to bottom right, rgb(203, 213, 225), rgb(148, 163, 184), rgb(100, 116, 139))';
          span.style.webkitBackgroundClip = 'text';
          span.style.backgroundClip = 'text';
          span.style.webkitTextFillColor = 'transparent';
          span.style.color = 'transparent';
        }

        element.appendChild(span);
        return span;
      });
    };

    // Function to split text into words
    const splitTextIntoWords = (element) => {
      const text = element.textContent;
      element.innerHTML = '';

      const words = text.split(' ');
      return words.map((word, index) => {
        const span = document.createElement('span');
        span.textContent = word;
        span.style.display = 'inline-block';
        span.style.opacity = '0';
        span.style.transform = 'translateY(20px)';
        span.style.color = 'inherit'; // Ensure color inheritance
        element.appendChild(span);

        // Add space after word (except last word)
        if (index < words.length - 1) {
          const space = document.createElement('span');
          space.textContent = '\u00A0';
          space.style.display = 'inline-block';
          space.style.opacity = '1'; // Make spaces visible
          element.appendChild(space);
        }

        return span;
      });
    };

    // Animate CONNECTING text - using simple fade in for gradient text
    if (connectingRef.current) {
      // Set initial state
      gsap.set(connectingRef.current, { opacity: 0, y: 40 });

      gsap.to(connectingRef.current, {
        opacity: 1,
        y: 0,
        duration: 0.8,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: connectingRef.current,
          start: 'top 80%',
          toggleActions: 'play none none none',
          once: true,
        }
      });
    }

    // Animate BRANDS TO text
    if (brandsRef.current) {
      const chars = splitTextIntoChars(brandsRef.current);
      gsap.to(chars, {
        opacity: 1,
        y: 0,
        duration: 0.6,
        stagger: 0.05,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: brandsRef.current,
          start: 'top 85%',
          toggleActions: 'play none none none',
          once: true,
        }
      });
    }

    // Animate THEIR CUSTOMERS text
    if (customersRef.current) {
      const chars = splitTextIntoChars(customersRef.current);
      gsap.to(chars, {
        opacity: 1,
        y: 0,
        duration: 0.6,
        stagger: 0.04,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: customersRef.current,
          start: 'top 90%',
          toggleActions: 'play none none none',
          once: true,
        }
      });
    }

    // Animate description text - using simple fade in first
    if (descriptionRef.current) {
      // Set initial state
      gsap.set(descriptionRef.current, { opacity: 0, y: 20 });

      gsap.to(descriptionRef.current, {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: descriptionRef.current,
          start: 'top 95%',
          toggleActions: 'play none none none',
          once: true,
        }
      });
    }

    // Cleanup
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <ClickSpark
      sparkColor='#fff'
      sparkSize={10}
      sparkRadius={15}
      sparkCount={8}
      duration={400}
    >
      <section className='min-h-fit py-8 sm:min-h-[40vh] lg:h-[60vh] bg-black flex flex-col lg:flex-row items-center justify-center px-4 sm:px-8 md:px-16 lg:px-24 xl:px-48 sm:py-8 lg:py-12'>
        <div className='flex-1 text-center lg:text-left mb-4 sm:mb-6 lg:mb-0'>
          <h1
            ref={connectingRef}
            className='text-[1.5rem] xs:text-[2rem] sm:text-[2.5rem] md:text-[2.8rem] lg:text-[3rem] xl:text-[4rem] 2xl:text-[5rem] font-bold bg-gradient-to-br from-slate-300 via-slate-400 to-slate-600 bg-clip-text text-transparent leading-none mb-2 sm:mb-3 lg:mb-4 text-center lg:text-left'
          >
            CONNECTING
          </h1>
          <div className='flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-3 sm:gap-4 lg:gap-6 mb-2 sm:mb-3 lg:mb-4'>
            <img
              className='rounded-2xl w-32 h-20 sm:w-40 sm:h-24 md:w-48 md:h-28 lg:w-56 lg:h-32 object-cover'
              src="https://html.rrdevs.net/faizen/assets/imgs/images/title-thumb-01.jpg"
              alt="Brand connection visual"
            />
            <h2
              ref={brandsRef}
              className='text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl 2xl:text-5xl font-bold text-white/90 uppercase text-center lg:text-left'
            >
              BRANDS TO
            </h2>
          </div>
          <h1
            ref={customersRef}
            className='text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl font-bold text-white/90 uppercase leading-tight text-center lg:text-left italic'
            style={{ fontFamily: 'Playfair Display, serif' }}
          >
            THEIR CUSTOMERS.
          </h1>
        </div>
        <div className='w-full lg:w-1/3 lg:ml-8 xl:ml-12 text-center lg:text-left mt-3 sm:mt-4 lg:mt-0'>
          <p
            ref={descriptionRef}
            className='text-xs sm:text-sm md:text-base lg:text-lg text-white/70 leading-relaxed uppercase max-w-xs sm:max-w-sm lg:max-w-md mx-auto lg:mx-0 block'
          >
            ORCI RUTOQUE PHASELLUS MAGNA DIS PORTTITOR MONTES, NASCETUR MUS ORNARE AUGUE MASSA LACINIA MAGNA COMMODO EROS ULTRICES RUTILA E ENIM IACULIS EGESTAS.
          </p>
        </div>
      </section>
    </ClickSpark>
  );
};

export default Connecting;
