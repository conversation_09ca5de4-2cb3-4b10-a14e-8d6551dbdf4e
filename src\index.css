@import "tailwindcss";

/* Smooth scrolling for entire website */
html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  font-family: 'Poppins', sans-serif;
  overflow-x: hidden;
}

/* Custom smooth scroll for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #000;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #64748b, #94a3b8);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #94a3b8, #cbd5e1);
}

/* Custom shining silver effects */
.silver-shine {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 25%, #cbd5e1 50%, #94a3b8 75%, #64748b 100%);
  background-size: 200% 200%;
  animation: silverShine 3s ease-in-out infinite;
}

@keyframes silverShine {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.metallic-text {
  background: linear-gradient(135deg, #64748b, #94a3b8, #cbd5e1, #e2e8f0, #f1f5f9);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: metallicShine 2s ease-in-out infinite;
}

@keyframes metallicShine {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Radial gradient utility */
.bg-gradient-radial {
  background: radial-gradient(circle, var(--tw-gradient-stops));
}
