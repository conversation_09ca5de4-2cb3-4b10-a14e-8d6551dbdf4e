import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const Project = () => {
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const containerRef = useRef(null);
  const cardsRef = useRef([]);

  // Sample project data
  const projects = [
    {
      id: 1,
      number: "01",
      title: "Digital Marketing Campaign",
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop"
    },
    {
      id: 2,
      number: "02",
      title: "E-commerce Platform",
      image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop"
    },
    {
      id: 3,
      number: "03",
      title: "Brand Identity Design",
      image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=300&fit=crop"
    },
    {
      id: 4,
      number: "04",
      title: "Mobile App Development",
      image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=300&fit=crop"
    }
  ];

  useEffect(() => {
    const section = sectionRef.current;
    const title = titleRef.current;
    const subtitle = subtitleRef.current;
    const container = containerRef.current;
    const cards = cardsRef.current;

    if (section && title && subtitle && container && cards.length > 0) {
      // Initial state - text starts large and at top
      gsap.set(container, {
        y: "-30vh", // Start at top of section
        scale: 1.2, // Start larger
        opacity: 1
      });

      // Text scroll animation - moves to center and scales down
      gsap.to(container, {
        y: 0, // Move to center
        scale: 0.8, // Scale down to medium size
        ease: "power1.out",
        scrollTrigger: {
          trigger: section,
          start: "top 70%",
          end: "center 60%",
          scrub: 1,
          markers: false
        }
      });

      // Set initial state for all cards - below screen
      gsap.set(cards, {
        y: "100vh",
        opacity: 1
      });

      // Create a timeline for cards animation to ensure all cards complete before allowing scroll
      const cardsTimeline = gsap.timeline({
        scrollTrigger: {
          trigger: section,
          start: "center center",
          end: "bottom+=100% center", // Extended end point to ensure all cards are shown
          scrub: 0.5,
          pin: true,
          markers: false,
          onComplete: () => {
            // All cards animation completed
            console.log("All cards animation completed");
          }
        }
      });

      // Add cards animation to timeline with stagger
      cardsTimeline.to(cards, {
        y: 0,
        duration: 1,
        stagger: 0.15, // Slightly faster stagger
        ease: "power2.out"
      });

      // Additional parallax effect for text to move behind cards - only after cards are shown
      gsap.to(container, {
        y: "30vh", // Move text further down behind cards
        scale: 0.5, // Scale down more
        opacity: 0.2, // Fade out more
        scrollTrigger: {
          trigger: section,
          start: "bottom 30%", // Start later to ensure cards are fully visible first
          end: "bottom top", // End when section bottom reaches viewport top
          scrub: 0.8,
          markers: false
        }
      });

      // Prevent scrolling past section until all animations complete
      ScrollTrigger.create({
        trigger: section,
        start: "top top",
        end: "bottom 5%", // Match the cards animation end point
        pin: false,
        anticipatePin: 1,
        onUpdate: (self) => {
          // Only allow scrolling past when cards animation is near completion
          if (self.progress > 0.9) {
            // Allow normal scrolling
            document.body.style.overflow = 'auto';
          }
        }
      });
    }

    // Cleanup function
    return () => {
      ScrollTrigger.getAll().forEach(trigger => {
        if (trigger.trigger === sectionRef.current) {
          trigger.kill();
        }
      });
    };
  }, []);

  return (
    <section
      ref={sectionRef}
      className="h-screen bg-black flex items-center justify-center px-4 sm:px-6 lg:px-8 relative overflow-hidden"
    >
      {/* Text Container - Behind cards */}
      <div
        ref={containerRef}
        className="max-w-7xl mx-auto text-center relative z-0"
      >
        <div className="space-y-4 sm:space-y-6 lg:space-y-8">
          {/* First row - "Have look at" - Poppins font - Reduced size */}
          <h2
            ref={titleRef}
            className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl 2xl:text-7xl font-bold text-white/90 uppercase tracking-wide"
            style={{ fontFamily: 'Poppins, sans-serif' }}
          >
            Have look at
          </h2>

          {/* Second row - "Our project" - Playfair font with italic */}
          <h1
            ref={subtitleRef}
            className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl 2xl:text-8xl font-bold text-white uppercase tracking-wide italic"
            style={{ fontFamily: 'Playfair Display, serif' }}
          >
            Our project
          </h1>
        </div>
      </div>

      {/* Cards Container - Above text */}
      <div className="absolute mb-80 inset-0 z-10 flex items-center justify-center">
        <div className="max-w-6xl mx-auto px-4 w-full">
          <div className="grid grid-cols-2 gap-8 lg:gap-12">
            {projects.map((project, index) => (
              <div
                key={project.id}
                ref={(el) => (cardsRef.current[index] = el)}
                className={`
                  bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-6 shadow-2xl
                  h-96 flex flex-col
                  ${index % 2 === 0 ? 'mt-0' : 'mt-16'} // Zigzag pattern - right column higher
                `}
              >
                {/* Number heading at top */}
                <div className="text-3xl font-bold text-white/60 mb-2">
                  {project.number}
                </div>

                {/* Title at top after number */}
                <h3 className="text-xl font-bold text-white mb-4">
                  {project.title}
                </h3>

                <div className="rounded-xl overflow-hidden flex-1">
                  <img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Project;
